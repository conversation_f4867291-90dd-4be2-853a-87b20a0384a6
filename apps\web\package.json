{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "npx next dev --turbopack", "build": "npx next build", "start": "npx next start", "lint": "npx next lint", "type-check": "tsc --noEmit", "clean": "rm -rf .next", "update-system-docs": "node scripts/update-system-docs.js", "add-restaurants": "node scripts/add-sample-restaurants.js", "clear-db": "node scripts/clear-database.js --confirm", "deploy-rules": "firebase deploy --only firestore:rules", "db:test": "curl http://localhost:3000/api/database/test", "cms:dashboard": "echo 'CMS Dashboard available at: http://localhost:3000/admin/cms-dashboard' && npm run dev", "email:setup": "node scripts/setup-resend-email.js", "email:test": "node scripts/setup-resend-email.js", "email:simple-test": "node src/tests/scripts/email/test-email-simple.js", "ai:demo": "echo 'AI Demo available at: http://localhost:3000/ai-demo' && npm run dev", "chatbot:start": "echo 'Chat<PERSON> is now active on all pages! Start dev server and look for the chat widget.' && npm run dev", "test:dashboard": "echo 'Test Dashboard available at: http://localhost:3000/tests/dashboard' && npm run dev", "test:auth": "echo 'Testing authentication at: http://localhost:3000/tests/pages/auth/test-auth' && npm run dev", "test:notifications": "echo 'Testing notifications at: http://localhost:3000/tests/pages/notifications/test-all-notifications' && npm run dev", "test:business": "echo 'Testing business logic at: http://localhost:3000/tests/pages/business/test-customer' && npm run dev", "test:all": "echo 'All tests available at: http://localhost:3000/tests/dashboard' && npm run dev", "test:enable": "echo 'ENABLE_TEST_ROUTES=true' >> .env.local && echo 'Test routes enabled for development'", "test:disable": "sed -i '/ENABLE_TEST_ROUTES=true/d' .env.local && echo 'Test routes disabled'", "test:verify-images": "node src/tests/scripts/database/verify-restaurant-images.js", "test:bonsai": "node src/tests/scripts/integrations/test-bonsai.js"}, "dependencies": {"@cloudinary/react": "^1.14.3", "@cloudinary/url-gen": "^1.21.0", "@elastic/elasticsearch": "^9.0.2", "@google/generative-ai": "^0.24.1", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@opensearch-project/opensearch": "^3.5.1", "@react-email/components": "^0.0.25", "@react-email/render": "^1.1.2", "@reduxjs/toolkit": "^2.5.0", "@supabase/supabase-js": "^2.50.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "@types/ws": "^8.18.1", "@upstash/redis": "^1.35.0", "api-client": "workspace:*", "axios": "^1.9.0", "business-logic": "workspace:*", "cloudinary": "^2.6.1", "config": "workspace:*", "crypto-js": "^4.2.0", "database": "workspace:*", "dotenv": "^16.5.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "firebase": "^11.8.0", "firebase-admin": "^13.4.0", "firebase-config": "workspace:*", "form-data": "^4.0.3", "ioredis": "^5.4.1", "lucide-react": "^0.511.0", "next": "15.3.2", "react": "19.0.0", "react-dom": "19.0.0", "react-dropzone": "^14.3.8", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "reselect": "^5.1.1", "resend": "^4.5.2", "shared-types": "workspace:*", "shared-ui": "workspace:*", "shared-utils": "workspace:*", "ws": "^8.18.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/google.maps": "^3.58.1", "@types/node": "^20.17.57", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "engines": {"node": "18.x || 20.x || 22.x", "npm": ">=8.0.0"}}