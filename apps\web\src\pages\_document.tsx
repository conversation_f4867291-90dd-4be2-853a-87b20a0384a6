import { Html, Head, Main, NextScript } from 'next/document';
import Document, { DocumentContext, DocumentInitialProps } from 'next/document';

// Force dynamic rendering to avoid React 19 SSR issues with styled-jsx
export const dynamic = 'force-dynamic';

export default function CustomDocument() {
  return (
    <Html lang="en">
      <Head />
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}

// Override getInitialProps to handle React 19 compatibility with styled-jsx
CustomDocument.getInitialProps = async (ctx: DocumentContext): Promise<DocumentInitialProps> => {
  const originalRenderPage = ctx.renderPage;

  // Wrap renderPage to handle styled-jsx context properly
  ctx.renderPage = () =>
    originalRenderPage({
      enhanceApp: (App: any) => (props: any) => {
        // Return App without styled-jsx wrapper to avoid React 19 useContext issues
        return <App {...props} />;
      },
    });

  // Get initial props without styled-jsx styles
  const initialProps = await Document.getInitialProps(ctx);

  return {
    ...initialProps,
    // Remove styled-jsx styles to prevent React 19 compatibility issues
    styles: null,
  };
};
